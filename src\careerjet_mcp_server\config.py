"""
Configuration management for Careerjet MCP Server.
"""

import os
from typing import Optional
from dotenv import load_dotenv
from .models import CareerjetConfig

# Load environment variables from .env file
load_dotenv()


def get_config() -> CareerjetConfig:
    """
    Get configuration from environment variables.
    
    Returns:
        CareerjetConfig: Configuration object with all settings
    """
    return CareerjetConfig(
        affiliate_id=os.getenv(
            "CAREERJET_AFFILIATE_ID", 
            "371d48447450886ce16b718533cca6f2"
        ),
        locale=os.getenv("CAREERJET_DEFAULT_LOCALE", "en_GB"),
        default_user_agent=os.getenv(
            "CAREERJET_DEFAULT_USER_AGENT",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        ),
        default_user_ip=os.getenv("CAREERJET_DEFAULT_USER_IP", "127.0.0.1"),
        default_url=os.getenv("CAREERJET_DEFAULT_URL", "http://localhost:3000/jobs"),
        rate_limit_requests=int(os.getenv("CAREERJET_RATE_LIMIT_REQUESTS", "100")),
        rate_limit_period=int(os.getenv("CAREERJET_RATE_LIMIT_PERIOD", "3600")),
        cache_ttl=int(os.getenv("CAREERJET_CACHE_TTL", "300")),
        timeout=int(os.getenv("CAREERJET_TIMEOUT", "30"))
    )


def get_mcp_config() -> dict:
    """
    Get MCP server configuration.
    
    Returns:
        dict: MCP server configuration
    """
    return {
        "name": os.getenv("MCP_SERVER_NAME", "Careerjet Job Search"),
        "version": os.getenv("MCP_SERVER_VERSION", "0.1.0"),
        "log_level": os.getenv("MCP_LOG_LEVEL", "INFO")
    }
