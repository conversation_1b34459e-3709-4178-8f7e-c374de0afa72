# Careerjet MCP Server - Deployment Guide

This guide covers deploying the Careerjet MCP Server to various platforms, with special focus on Smithery.ai deployment.

## 🚀 Smithery.ai Deployment

Smithery.ai is the recommended platform for deploying MCP servers. It provides automatic scaling, configuration management, and easy integration with MCP clients.

### Prerequisites

1. **GitHub Repository**: Your code must be in a GitHub repository
2. **Smithery Account**: Sign up at [smithery.ai](https://smithery.ai)
3. **Careerjet Affiliate ID**: Use the provided ID `371d48447450886ce16b718533cca6f2`

### Deployment Steps

1. **Push Code to GitHub**:
   ```bash
   git add .
   git commit -m "Add Careerjet MCP Server implementation"
   git push origin main
   ```

2. **Connect to Smithery**:
   - Log in to [smithery.ai](https://smithery.ai)
   - Connect your GitHub account
   - Select the CareerjetMCP repository

3. **Configure Deployment**:
   - <PERSON><PERSON> will automatically detect the `smithery.yaml` configuration
   - The server will be built using the provided `Dockerfile`
   - Configuration options will be available through the Smithery UI

4. **Set Configuration**:
   - **affiliate_id**: `371d48447450886ce16b718533cca6f2` (default)
   - **locale**: Choose your preferred locale (e.g., `en_GB`, `en_US`)
   - **user_ip**: Your actual IP address for production use
   - **base_url**: Your application's URL
   - Other settings can use defaults

5. **Deploy**:
   - Click "Deploy" in the Smithery dashboard
   - Wait for the build and deployment to complete
   - Your server will be available at a Smithery-provided URL

### Configuration Options

The server supports the following configuration options through Smithery:

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `affiliate_id` | string | `371d48447450886ce16b718533cca6f2` | Careerjet affiliate ID |
| `locale` | string | `en_GB` | Default locale for searches |
| `user_agent` | string | Mozilla/5.0... | User agent for API requests |
| `user_ip` | string | `127.0.0.1` | IP address for API requests |
| `base_url` | string | `https://your-app.com` | Base URL for your app |
| `rate_limit_requests` | integer | `100` | Max requests per period |
| `rate_limit_period` | integer | `3600` | Rate limit period (seconds) |
| `cache_ttl` | integer | `300` | Cache TTL (seconds) |
| `timeout` | integer | `30` | Request timeout (seconds) |

### Using the Deployed Server

Once deployed, you can use the server in your MCP clients:

```json
{
  "mcpServers": {
    "careerjet": {
      "command": "smithery",
      "args": ["@your-username/careerjet-mcp-server"],
      "env": {
        "SMITHERY_API_KEY": "your-smithery-api-key"
      }
    }
  }
}
```

## 🐳 Docker Deployment

For self-hosting or custom deployments:

### Build and Run

```bash
# Build the Docker image
docker build -t careerjet-mcp-server .

# Run the container
docker run -d \
  --name careerjet-mcp \
  -p 8000:8000 \
  -e CAREERJET_AFFILIATE_ID=371d48447450886ce16b718533cca6f2 \
  -e CAREERJET_DEFAULT_LOCALE=en_GB \
  -e CAREERJET_DEFAULT_USER_IP=your.actual.ip.address \
  -e CAREERJET_DEFAULT_URL=https://your-app.com \
  careerjet-mcp-server
```

### Docker Compose

```yaml
version: '3.8'
services:
  careerjet-mcp:
    build: .
    ports:
      - "8000:8000"
    environment:
      - CAREERJET_AFFILIATE_ID=371d48447450886ce16b718533cca6f2
      - CAREERJET_DEFAULT_LOCALE=en_GB
      - CAREERJET_DEFAULT_USER_IP=your.actual.ip.address
      - CAREERJET_DEFAULT_URL=https://your-app.com
      - CAREERJET_RATE_LIMIT_REQUESTS=100
      - CAREERJET_CACHE_TTL=300
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "health_check.py"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

## 🔧 Local Development

For local development and testing:

### Setup

```bash
# Clone the repository
git clone https://github.com/rftsngl/CareerjetMCP.git
cd CareerjetMCP

# Install dependencies
pip install -r requirements.txt
pip install -e .

# Set up environment
cp .env.example .env
# Edit .env with your configuration
```

### Run Locally

```bash
# Run the server
python -m careerjet_mcp_server.server

# Or use the entry point
careerjet-mcp-server
```

### Test with MCP Inspector

```bash
# Install MCP Inspector
npm install -g @modelcontextprotocol/inspector

# Test the server
mcp-inspector python -m careerjet_mcp_server.server
```

## 🌐 Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `CAREERJET_AFFILIATE_ID` | Careerjet affiliate ID | `371d48447450886ce16b718533cca6f2` |
| `CAREERJET_DEFAULT_LOCALE` | Default locale | `en_GB` |
| `CAREERJET_DEFAULT_USER_AGENT` | User agent string | Mozilla/5.0... |
| `CAREERJET_DEFAULT_USER_IP` | User IP address | `127.0.0.1` |
| `CAREERJET_DEFAULT_URL` | Base URL | `http://localhost:3000` |
| `CAREERJET_RATE_LIMIT_REQUESTS` | Rate limit requests | `100` |
| `CAREERJET_RATE_LIMIT_PERIOD` | Rate limit period | `3600` |
| `CAREERJET_CACHE_TTL` | Cache TTL | `300` |
| `CAREERJET_TIMEOUT` | Request timeout | `30` |

## 🔍 Troubleshooting

### Common Issues

1. **"Invalid affiliate ID" error**:
   - Ensure you're using the correct affiliate ID: `371d48447450886ce16b718533cca6f2`
   - Check that the ID is properly set in your configuration

2. **Rate limiting errors**:
   - Reduce the rate limit settings
   - Increase the cache TTL to reduce API calls

3. **Connection timeouts**:
   - Increase the timeout setting
   - Check your network connectivity

4. **Docker health check failures**:
   - Ensure the health check script is executable
   - Check container logs for errors

### Debugging

```bash
# Check container logs
docker logs careerjet-mcp

# Run health check manually
docker exec careerjet-mcp python health_check.py

# Test API connectivity
docker exec careerjet-mcp python -c "
from src.careerjet_mcp_server.careerjet_client import CareerjetClient
from src.careerjet_mcp_server.models import CareerjetConfig
import asyncio

async def test():
    config = CareerjetConfig(affiliate_id='371d48447450886ce16b718533cca6f2')
    async with CareerjetClient(config) as client:
        result = await client.search_jobs('python developer', 'London')
        print(f'Found {result.total_hits} jobs')

asyncio.run(test())
"
```

## 📊 Monitoring

### Health Checks

The server includes built-in health checks:

- **HTTP Health Check**: `GET /health` (if running in HTTP mode)
- **Docker Health Check**: Automatic container health monitoring
- **Application Health**: Monitors client initialization and API connectivity

### Metrics

Monitor these key metrics:

- Request rate and response times
- Cache hit/miss ratios
- API error rates
- Memory and CPU usage

## 🔒 Security Considerations

1. **API Keys**: Never commit API keys to version control
2. **Rate Limiting**: Configure appropriate rate limits
3. **Network Security**: Use HTTPS in production
4. **Container Security**: Run as non-root user (already configured)
5. **Input Validation**: All inputs are validated (already implemented)

## 📈 Scaling

For high-traffic deployments:

1. **Horizontal Scaling**: Deploy multiple instances behind a load balancer
2. **Caching**: Increase cache TTL and consider external caching (Redis)
3. **Rate Limiting**: Implement distributed rate limiting
4. **Monitoring**: Use APM tools for performance monitoring

## 🆘 Support

For deployment issues:

1. Check the [GitHub Issues](https://github.com/rftsngl/CareerjetMCP/issues)
2. Review the logs for error messages
3. Test with MCP Inspector for debugging
4. Contact Smithery support for platform-specific issues
