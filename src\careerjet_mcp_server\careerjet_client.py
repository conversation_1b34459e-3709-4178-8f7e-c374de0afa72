"""
Careerjet API client implementation.
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple
from urllib.parse import urlencode

import httpx
from pydantic import ValidationError

from .models import (
    APIError,
    CareerjetConfig,
    JobListing,
    JobSearchFilter,
    SearchParameters,
    SearchResults,
)

logger = logging.getLogger(__name__)


class CareerjetAPIError(Exception):
    """Exception raised for Careerjet API errors."""
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.error_code = error_code
        self.details = details or {}


class RateLimiter:
    """Simple rate limiter for API requests."""
    
    def __init__(self, max_requests: int, period: int):
        self.max_requests = max_requests
        self.period = period
        self.requests: List[datetime] = []
        self._lock = asyncio.Lock()
    
    async def acquire(self) -> None:
        """Acquire permission to make a request."""
        async with self._lock:
            now = datetime.now()
            # Remove old requests outside the period
            cutoff = now - timedelta(seconds=self.period)
            self.requests = [req_time for req_time in self.requests if req_time > cutoff]
            
            if len(self.requests) >= self.max_requests:
                # Calculate wait time
                oldest_request = min(self.requests)
                wait_time = (oldest_request + timedelta(seconds=self.period) - now).total_seconds()
                if wait_time > 0:
                    logger.info(f"Rate limit reached, waiting {wait_time:.2f} seconds")
                    await asyncio.sleep(wait_time)
            
            self.requests.append(now)


class CareerjetClient:
    """Async client for Careerjet API."""
    
    # Locale to API endpoint mapping
    LOCALE_ENDPOINTS = {
        'cs_CZ': 'http://www.careerjet.cz',
        'da_DK': 'http://www.careerjet.dk',
        'de_AT': 'http://www.careerjet.at',
        'de_CH': 'http://www.careerjet.ch',
        'de_DE': 'http://www.careerjet.de',
        'en_AE': 'http://www.careerjet.ae',
        'en_AU': 'http://www.careerjet.com.au',
        'en_BD': 'http://www.careerjet.com.bd',
        'en_CA': 'http://www.careerjet.ca',
        'en_CN': 'http://www.careerjet.com.cn',
        'en_HK': 'http://www.careerjet.hk',
        'en_IE': 'http://www.careerjet.ie',
        'en_IN': 'http://www.careerjet.co.in',
        'en_KW': 'http://www.careerjet.com.kw',
        'en_MY': 'http://www.careerjet.com.my',
        'en_NZ': 'http://www.careerjet.co.nz',
        'en_OM': 'http://www.careerjet.com.om',
        'en_PH': 'http://www.careerjet.ph',
        'en_PK': 'http://www.careerjet.com.pk',
        'en_QA': 'http://www.careerjet.com.qa',
        'en_SG': 'http://www.careerjet.sg',
        'en_GB': 'http://www.careerjet.co.uk',
        'en_US': 'http://www.careerjet.com',
        'en_ZA': 'http://www.careerjet.co.za',
        'en_SA': 'http://www.careerjet-saudi-arabia.com',
        'en_TW': 'http://www.careerjet.com.tw',
        'en_VN': 'http://www.careerjet.vn',
        'es_AR': 'http://www.opcionempleo.com.ar',
        'es_BO': 'http://www.opcionempleo.com.bo',
        'es_CL': 'http://www.opcionempleo.cl',
        'es_CO': 'http://www.opcionempleo.com.co',
        'es_CR': 'http://www.opcionempleo.co.cr',
        'es_DO': 'http://www.opcionempleo.com.do',
        'es_EC': 'http://www.opcionempleo.ec',
        'es_ES': 'http://www.opcionempleo.com',
        'es_GT': 'http://www.opcionempleo.com.gt',
        'es_MX': 'http://www.opcionempleo.com.mx',
        'es_PA': 'http://www.opcionempleo.com.pa',
        'es_PE': 'http://www.opcionempleo.com.pe',
        'es_PR': 'http://www.opcionempleo.com.pr',
        'es_PY': 'http://www.opcionempleo.com.py',
        'es_UY': 'http://www.opcionempleo.com.uy',
        'es_VE': 'http://www.opcionempleo.com.ve',
        'fi_FI': 'http://www.careerjet.fi',
        'fr_CA': 'http://www.option-carriere.ca',
        'fr_BE': 'http://www.optioncarriere.be',
        'fr_CH': 'http://www.optioncarriere.ch',
        'fr_FR': 'http://www.optioncarriere.com',
        'fr_LU': 'http://www.optioncarriere.lu',
        'fr_MA': 'http://www.optioncarriere.ma',
        'hu_HU': 'http://www.careerjet.hu',
        'it_IT': 'http://www.careerjet.it',
        'ja_JP': 'http://www.careerjet.jp',
        'ko_KR': 'http://www.careerjet.co.kr',
        'nl_BE': 'http://www.careerjet.be',
        'nl_NL': 'http://www.careerjet.nl',
        'no_NO': 'http://www.careerjet.no',
        'pl_PL': 'http://www.careerjet.pl',
        'pt_PT': 'http://www.careerjet.pt',
        'pt_BR': 'http://www.careerjet.com.br',
        'ru_RU': 'http://www.careerjet.ru',
        'ru_UA': 'http://www.careerjet.com.ua',
        'sv_SE': 'http://www.careerjet.se',
        'sk_SK': 'http://www.careerjet.sk',
        'tr_TR': 'http://www.careerjet.com.tr',
        'uk_UA': 'http://www.careerjet.ua',
        'vi_VN': 'http://www.careerjet.com.vn',
        'zh_CN': 'http://www.careerjet.cn',
    }
    
    def __init__(self, config: CareerjetConfig):
        self.config = config
        self.rate_limiter = RateLimiter(
            config.rate_limit_requests,
            config.rate_limit_period
        )
        self._client: Optional[httpx.AsyncClient] = None
        self._cache: Dict[str, Tuple[datetime, Any]] = {}
    
    async def __aenter__(self):
        """Async context manager entry."""
        self._client = httpx.AsyncClient(
            timeout=httpx.Timeout(self.config.timeout),
            headers={
                'User-Agent': self.config.default_user_agent,
                'Accept': 'application/json',
                'Accept-Encoding': 'gzip, deflate',
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self._client:
            await self._client.aclose()
    
    def _get_api_endpoint(self) -> str:
        """Get the API endpoint for the configured locale."""
        base_url = self.LOCALE_ENDPOINTS.get(self.config.locale)
        if not base_url:
            raise CareerjetAPIError(f"Unsupported locale: {self.config.locale}")
        return f"{base_url}/api/jobs"
    
    def _get_cache_key(self, params: Dict[str, Any]) -> str:
        """Generate cache key for request parameters."""
        # Sort parameters for consistent cache keys
        sorted_params = sorted(params.items())
        return f"search_{hash(str(sorted_params))}"
    
    def _get_cached_result(self, cache_key: str) -> Optional[Any]:
        """Get cached result if still valid."""
        if cache_key in self._cache:
            cached_time, cached_result = self._cache[cache_key]
            if datetime.now() - cached_time < timedelta(seconds=self.config.cache_ttl):
                logger.debug(f"Cache hit for key: {cache_key}")
                return cached_result
            else:
                # Remove expired cache entry
                del self._cache[cache_key]
        return None
    
    def _cache_result(self, cache_key: str, result: Any) -> None:
        """Cache the result."""
        self._cache[cache_key] = (datetime.now(), result)
        logger.debug(f"Cached result for key: {cache_key}")
    
    async def _make_request(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Make HTTP request to Careerjet API."""
        if not self._client:
            raise CareerjetAPIError("Client not initialized. Use async context manager.")
        
        # Check cache first
        cache_key = self._get_cache_key(params)
        cached_result = self._get_cached_result(cache_key)
        if cached_result:
            return cached_result
        
        # Apply rate limiting
        await self.rate_limiter.acquire()
        
        api_endpoint = self._get_api_endpoint()
        
        try:
            logger.debug(f"Making request to {api_endpoint} with params: {params}")
            response = await self._client.get(api_endpoint, params=params)
            response.raise_for_status()
            
            result = response.json()
            
            # Check for API errors in response
            if 'error' in result:
                raise CareerjetAPIError(
                    result.get('error', 'Unknown API error'),
                    error_code=result.get('error_code'),
                    details=result
                )
            
            # Cache successful result
            self._cache_result(cache_key, result)
            
            return result
            
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error: {e.response.status_code} - {e.response.text}")
            raise CareerjetAPIError(
                f"HTTP {e.response.status_code}: {e.response.text}",
                error_code=str(e.response.status_code)
            )
        except httpx.RequestError as e:
            logger.error(f"Request error: {e}")
            raise CareerjetAPIError(f"Request failed: {e}")
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            raise CareerjetAPIError(f"Invalid JSON response: {e}")
    
    def _parse_job_listing(self, job_data: Dict[str, Any]) -> JobListing:
        """Parse job data from API response into JobListing model."""
        try:
            return JobListing(
                job_id=job_data.get('jobid', ''),
                title=job_data.get('jobtitle', ''),
                company=job_data.get('company', ''),
                location=job_data.get('locations', ''),
                description=job_data.get('jobdescription', ''),
                url=job_data.get('url', ''),
                salary=job_data.get('salary'),
                contract_type=job_data.get('contracttype'),
                contract_time=job_data.get('contracttime'),
                date_posted=job_data.get('date')
            )
        except ValidationError as e:
            logger.error(f"Failed to parse job listing: {e}")
            raise CareerjetAPIError(f"Invalid job data: {e}")
    
    async def search_jobs(
        self,
        keywords: Optional[str] = None,
        location: Optional[str] = None,
        sort: str = "relevance",
        page: int = 1,
        pagesize: int = 20,
        contracttype: Optional[str] = None,
        contractperiod: Optional[str] = None,
        user_ip: Optional[str] = None,
        user_agent: Optional[str] = None,
        url: Optional[str] = None,
        filters: Optional[JobSearchFilter] = None
    ) -> SearchResults:
        """
        Search for jobs using Careerjet API.
        
        Args:
            keywords: Keywords to search for
            location: Location to search in
            sort: Sort order (relevance, date, salary)
            page: Page number
            pagesize: Number of results per page
            contracttype: Contract type filter
            contractperiod: Contract period filter
            user_ip: User IP address (required by API)
            user_agent: User agent string (required by API)
            url: URL of page displaying results (required by API)
            filters: Additional filtering options
        
        Returns:
            SearchResults object containing job listings and metadata
        """
        # Build search parameters
        params = SearchParameters(
            keywords=keywords,
            location=location,
            sort=sort,
            page=page,
            pagesize=pagesize,
            contracttype=contracttype,
            contractperiod=contractperiod,
            affid=self.config.affiliate_id,
            user_ip=user_ip or self.config.default_user_ip,
            user_agent=user_agent or self.config.default_user_agent,
            url=url or self.config.default_url
        )
        
        # Convert to dict for API request
        api_params = params.dict(exclude_none=True)
        
        # Make API request
        response_data = await self._make_request(api_params)
        
        # Parse response
        jobs_data = response_data.get('jobs', [])
        jobs = []
        
        for job_data in jobs_data:
            try:
                job = self._parse_job_listing(job_data)
                
                # Apply additional filters if provided
                if filters and not self._apply_filters(job, filters):
                    continue
                    
                jobs.append(job)
            except CareerjetAPIError:
                # Skip invalid job listings but continue processing
                continue
        
        # Build search results
        total_hits = response_data.get('hits', len(jobs))
        total_pages = response_data.get('pages', 1)
        
        return SearchResults(
            total_hits=total_hits,
            total_pages=total_pages,
            current_page=page,
            jobs=jobs,
            search_params=api_params
        )
    
    def _apply_filters(self, job: JobListing, filters: JobSearchFilter) -> bool:
        """Apply additional filters to job listing."""
        # Company name filter
        if filters.company_names:
            if not any(company.lower() in job.company.lower() for company in filters.company_names):
                return False
        
        # Exclude keywords filter
        if filters.exclude_keywords:
            job_text = f"{job.title} {job.description} {job.company}".lower()
            if any(keyword.lower() in job_text for keyword in filters.exclude_keywords):
                return False
        
        # Remote job filter
        if filters.remote_only:
            job_text = f"{job.title} {job.description} {job.location}".lower()
            remote_keywords = ['remote', 'work from home', 'telecommute', 'home office']
            if not any(keyword in job_text for keyword in remote_keywords):
                return False
        
        return True
    
    async def get_job_details(self, job_id: str) -> Optional[JobListing]:
        """
        Get detailed information for a specific job.
        Note: This is a placeholder as Careerjet API doesn't have a specific job details endpoint.
        """
        # Since Careerjet doesn't have a job details endpoint, we return None
        # In a real implementation, you might search for the job by ID or use the job URL
        logger.warning(f"Job details endpoint not available for job_id: {job_id}")
        return None
    
    def clear_cache(self) -> None:
        """Clear the request cache."""
        self._cache.clear()
        logger.info("Cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        now = datetime.now()
        valid_entries = 0
        expired_entries = 0
        
        for cached_time, _ in self._cache.values():
            if now - cached_time < timedelta(seconds=self.config.cache_ttl):
                valid_entries += 1
            else:
                expired_entries += 1
        
        return {
            'total_entries': len(self._cache),
            'valid_entries': valid_entries,
            'expired_entries': expired_entries,
            'cache_ttl': self.config.cache_ttl
        }
