"""
Data models for Careerjet API responses and MCP server types.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field, validator


class JobListing(BaseModel):
    """Represents a single job listing from Careerjet API."""
    
    job_id: str = Field(..., description="Unique job identifier")
    title: str = Field(..., description="Job title")
    company: str = Field(..., description="Company name")
    location: str = Field(..., description="Job location")
    description: str = Field(..., description="Job description")
    url: str = Field(..., description="URL to the full job posting")
    salary: Optional[str] = Field(None, description="Salary information if available")
    contract_type: Optional[str] = Field(None, description="Contract type (permanent, contract, etc.)")
    contract_time: Optional[str] = Field(None, description="Contract time (full-time, part-time, etc.)")
    date_posted: Optional[str] = Field(None, description="Date when job was posted")
    
    @validator('description')
    def clean_description(cls, v: str) -> str:
        """Clean HTML tags and excessive whitespace from description."""
        import re
        # Remove HTML tags
        v = re.sub(r'<[^>]+>', '', v)
        # Clean up whitespace
        v = re.sub(r'\s+', ' ', v).strip()
        return v


class SearchResults(BaseModel):
    """Represents search results from Careerjet API."""
    
    total_hits: int = Field(..., description="Total number of jobs found")
    total_pages: int = Field(..., description="Total number of pages")
    current_page: int = Field(..., description="Current page number")
    jobs: List[JobListing] = Field(..., description="List of job listings")
    search_params: Dict[str, Any] = Field(..., description="Parameters used for the search")
    
    @property
    def has_more_pages(self) -> bool:
        """Check if there are more pages available."""
        return self.current_page < self.total_pages


class SearchParameters(BaseModel):
    """Parameters for job search requests."""
    
    keywords: Optional[str] = Field(None, description="Keywords to search for")
    location: Optional[str] = Field(None, description="Location to search in")
    sort: Optional[str] = Field("relevance", description="Sort order: relevance, date, or salary")
    start_num: Optional[int] = Field(1, description="Starting position for results")
    pagesize: Optional[int] = Field(20, description="Number of results per page")
    page: Optional[int] = Field(None, description="Page number (overrides start_num)")
    contracttype: Optional[str] = Field(None, description="Contract type filter")
    contractperiod: Optional[str] = Field(None, description="Contract period filter")
    
    # Mandatory parameters for API
    affid: str = Field(..., description="Affiliate ID")
    user_ip: str = Field(..., description="User IP address")
    user_agent: str = Field(..., description="User agent string")
    url: str = Field(..., description="URL of the page displaying results")
    
    @validator('sort')
    def validate_sort(cls, v: Optional[str]) -> Optional[str]:
        """Validate sort parameter."""
        if v and v not in ['relevance', 'date', 'salary']:
            raise ValueError("Sort must be one of: relevance, date, salary")
        return v
    
    @validator('contracttype')
    def validate_contracttype(cls, v: Optional[str]) -> Optional[str]:
        """Validate contract type parameter."""
        if v and v not in ['p', 'c', 't', 'i', 'v']:
            raise ValueError("Contract type must be one of: p (permanent), c (contract), t (temporary), i (training), v (voluntary)")
        return v
    
    @validator('contractperiod')
    def validate_contractperiod(cls, v: Optional[str]) -> Optional[str]:
        """Validate contract period parameter."""
        if v and v not in ['f', 'p']:
            raise ValueError("Contract period must be one of: f (full-time), p (part-time)")
        return v
    
    @validator('pagesize')
    def validate_pagesize(cls, v: Optional[int]) -> Optional[int]:
        """Validate page size parameter."""
        if v and (v < 1 or v > 100):
            raise ValueError("Page size must be between 1 and 100")
        return v


class CareerjetConfig(BaseModel):
    """Configuration for Careerjet API client."""
    
    affiliate_id: str = Field(..., description="Careerjet affiliate ID")
    locale: str = Field("en_GB", description="Locale code for API endpoint")
    default_user_agent: str = Field(
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        description="Default user agent string"
    )
    default_user_ip: str = Field("127.0.0.1", description="Default user IP address")
    default_url: str = Field("http://localhost:3000/jobs", description="Default URL for job listings")
    rate_limit_requests: int = Field(100, description="Rate limit: requests per period")
    rate_limit_period: int = Field(3600, description="Rate limit period in seconds")
    cache_ttl: int = Field(300, description="Cache TTL in seconds")
    timeout: int = Field(30, description="HTTP request timeout in seconds")
    
    @validator('locale')
    def validate_locale(cls, v: str) -> str:
        """Validate locale code."""
        valid_locales = {
            'cs_CZ', 'da_DK', 'de_AT', 'de_CH', 'de_DE', 'en_AE', 'en_AU', 'en_BD',
            'en_CA', 'en_CN', 'en_HK', 'en_IE', 'en_IN', 'en_KW', 'en_MY', 'en_NZ',
            'en_OM', 'en_PH', 'en_PK', 'en_QA', 'en_SG', 'en_GB', 'en_US', 'en_ZA',
            'en_SA', 'en_TW', 'en_VN', 'es_AR', 'es_BO', 'es_CL', 'es_CO', 'es_CR',
            'es_DO', 'es_EC', 'es_ES', 'es_GT', 'es_MX', 'es_PA', 'es_PE', 'es_PR',
            'es_PY', 'es_UY', 'es_VE', 'fi_FI', 'fr_CA', 'fr_BE', 'fr_CH', 'fr_FR',
            'fr_LU', 'fr_MA', 'hu_HU', 'it_IT', 'ja_JP', 'ko_KR', 'nl_BE', 'nl_NL',
            'no_NO', 'pl_PL', 'pt_PT', 'pt_BR', 'ru_RU', 'ru_UA', 'sv_SE', 'sk_SK',
            'tr_TR', 'uk_UA', 'vi_VN', 'zh_CN'
        }
        if v not in valid_locales:
            raise ValueError(f"Invalid locale: {v}. Must be one of: {', '.join(sorted(valid_locales))}")
        return v


class APIError(BaseModel):
    """Represents an API error response."""
    
    error_code: str = Field(..., description="Error code")
    error_message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")


class JobSearchFilter(BaseModel):
    """Advanced filtering options for job searches."""
    
    min_salary: Optional[int] = Field(None, description="Minimum salary filter")
    max_salary: Optional[int] = Field(None, description="Maximum salary filter")
    company_names: Optional[List[str]] = Field(None, description="Filter by specific company names")
    exclude_keywords: Optional[List[str]] = Field(None, description="Keywords to exclude from results")
    date_range_days: Optional[int] = Field(None, description="Only show jobs posted within N days")
    remote_only: Optional[bool] = Field(None, description="Filter for remote jobs only")
