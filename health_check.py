#!/usr/bin/env python3
"""
Health check script for Docker health checks.
Tests HTTP endpoint when in HTTP mode, otherwise tests module import.
"""

import sys
import os
import urllib.request

# Add the src directory to Python path for module imports
src_path = os.path.join(os.path.dirname(__file__), 'src')
if src_path not in sys.path:
    sys.path.insert(0, src_path)

# Import the server module at top level to avoid import issues
try:
    import careerjet_mcp_server.server as server_module
    SERVER_MODULE_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Could not import server module: {e}")
    server_module = None
    SERVER_MODULE_AVAILABLE = False

def check_http_endpoint():
    """Check if the HTTP endpoint is responding."""
    try:
        port = os.getenv('PORT', '8000')
        url = f"http://localhost:{port}/mcp"

        # Create a simple request to test if server is responding
        req = urllib.request.Request(url)
        req.add_header('Content-Type', 'application/json')

        with urllib.request.urlopen(req, timeout=5) as response:
            if response.status == 200:
                print("HTTP MCP Server is healthy")
                return True
    except Exception as e:
        print(f"HTTP health check failed: {e}")
        return False

    return False

def check_module_import():
    """Check if the server module can be imported."""
    if not SERVER_MODULE_AVAILABLE or server_module is None:
        print("Module import health check failed: Server module not available")
        return False

    try:
        # Check if the app object exists
        if hasattr(server_module, 'app'):
            app = server_module.app
            if hasattr(app, 'name'):
                print(f"MCP Server module is healthy - {app.name}")
            else:
                print("MCP Server module is healthy")
            return True

        print("MCP Server module missing app object")
        return False
    except Exception as e:
        print(f"Module import health check failed: {e}")
        return False

# Main health check logic
transport_mode = os.getenv('MCP_TRANSPORT', 'stdio')

if transport_mode == 'http':
    # For HTTP mode, test the endpoint
    if check_http_endpoint():
        sys.exit(0)
    else:
        sys.exit(1)
else:
    # For stdio mode, test module import
    if check_module_import():
        sys.exit(0)
    else:
        sys.exit(1)
