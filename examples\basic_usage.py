#!/usr/bin/env python3
"""
Basic usage examples for Careerjet MCP Server.

This script demonstrates how to use the Careerjet MCP Server
for job searching and data retrieval.
"""

import asyncio
import json
from typing import Any, Dict

# Note: This is a conceptual example. In practice, you would use
# an MCP client library to interact with the server.


async def example_job_search():
    """Example of basic job search."""
    print("=== Basic Job Search ===")
    
    # Simulated MCP tool call
    search_request = {
        "keywords": "python developer",
        "location": "London",
        "sort": "relevance",
        "page": 1,
        "pagesize": 10
    }
    
    print(f"Searching for jobs with parameters: {json.dumps(search_request, indent=2)}")
    
    # In a real MCP client, this would be:
    # result = await mcp_client.call_tool("search_jobs", search_request)
    
    print("Expected response structure:")
    print("""
    {
        "total_hits": 150,
        "total_pages": 15,
        "current_page": 1,
        "has_more_pages": true,
        "jobs": [
            {
                "job_id": "12345",
                "title": "Senior Python Developer",
                "company": "Tech Corp",
                "location": "London, UK",
                "description": "We are looking for an experienced Python developer...",
                "url": "https://www.careerjet.co.uk/jobad/...",
                "salary": "£50,000 - £70,000",
                "contract_type": "p",
                "contract_time": "f",
                "date_posted": "2024-01-15"
            }
        ]
    }
    """)


async def example_filtered_search():
    """Example of advanced filtered job search."""
    print("\n=== Advanced Filtered Search ===")
    
    search_params = {
        "keywords": "software engineer",
        "location": "San Francisco",
        "sort": "salary",
        "page": 1,
        "pagesize": 20,
        "contracttype": "p",  # permanent only
        "contractperiod": "f"  # full-time only
    }
    
    filter_params = {
        "company_names": ["Google", "Apple", "Microsoft", "Meta"],
        "exclude_keywords": ["junior", "intern", "entry-level"],
        "remote_only": True
    }
    
    request = {
        "search": search_params,
        "filters": filter_params
    }
    
    print(f"Filtered search request: {json.dumps(request, indent=2)}")
    
    # In a real MCP client:
    # result = await mcp_client.call_tool("search_jobs_filtered", request)


async def example_resource_access():
    """Example of accessing job data as MCP resources."""
    print("\n=== Resource Access Examples ===")
    
    resources = [
        "careerjet://search/python developer",
        "careerjet://search/data scientist/location/New York",
        "careerjet://recent/London",
        "careerjet://config"
    ]
    
    for resource_uri in resources:
        print(f"\nAccessing resource: {resource_uri}")
        # In a real MCP client:
        # content = await mcp_client.read_resource(resource_uri)
        # print(f"Content preview: {content[:200]}...")


async def example_pagination():
    """Example of handling paginated results."""
    print("\n=== Pagination Example ===")
    
    # First page
    page_1_request = {
        "keywords": "machine learning",
        "location": "California",
        "page": 1,
        "pagesize": 25
    }
    
    print(f"Page 1 request: {json.dumps(page_1_request, indent=2)}")
    
    # Simulate getting results and checking for more pages
    print("Processing results...")
    print("Checking if has_more_pages: true")
    
    # Next page
    page_2_request = {
        "keywords": "machine learning",
        "location": "California",
        "page": 2,
        "pagesize": 25
    }
    
    print(f"Page 2 request: {json.dumps(page_2_request, indent=2)}")


async def example_error_handling():
    """Example of error handling scenarios."""
    print("\n=== Error Handling Examples ===")
    
    # Invalid parameters
    invalid_requests = [
        {
            "description": "Invalid sort parameter",
            "request": {
                "keywords": "developer",
                "sort": "invalid_sort"
            }
        },
        {
            "description": "Page size too large",
            "request": {
                "keywords": "developer",
                "pagesize": 200
            }
        },
        {
            "description": "Invalid contract type",
            "request": {
                "keywords": "developer",
                "contracttype": "invalid"
            }
        }
    ]
    
    for example in invalid_requests:
        print(f"\n{example['description']}:")
        print(f"Request: {json.dumps(example['request'], indent=2)}")
        print("Expected error: ValidationError with specific message")


async def example_cache_management():
    """Example of cache management operations."""
    print("\n=== Cache Management ===")
    
    print("Getting cache statistics:")
    # result = await mcp_client.call_tool("get_cache_stats")
    print("Expected response:")
    print("""
    {
        "total_entries": 15,
        "valid_entries": 12,
        "expired_entries": 3,
        "cache_ttl": 300
    }
    """)
    
    print("\nClearing cache:")
    # result = await mcp_client.call_tool("clear_cache")
    print("Expected response: 'Cache cleared successfully. Next searches will fetch fresh results.'")


async def example_contract_filters():
    """Example of using different contract type and period filters."""
    print("\n=== Contract Filter Examples ===")
    
    contract_examples = [
        {
            "description": "Permanent full-time positions",
            "contracttype": "p",
            "contractperiod": "f"
        },
        {
            "description": "Contract positions (any time)",
            "contracttype": "c",
            "contractperiod": None
        },
        {
            "description": "Part-time positions (any type)",
            "contracttype": None,
            "contractperiod": "p"
        },
        {
            "description": "Temporary full-time positions",
            "contracttype": "t",
            "contractperiod": "f"
        }
    ]
    
    for example in contract_examples:
        print(f"\n{example['description']}:")
        request = {
            "keywords": "developer",
            "location": "London"
        }
        if example["contracttype"]:
            request["contracttype"] = example["contracttype"]
        if example["contractperiod"]:
            request["contractperiod"] = example["contractperiod"]
        
        print(f"Request: {json.dumps(request, indent=2)}")


async def example_locale_usage():
    """Example of using different locales."""
    print("\n=== Locale Usage Examples ===")
    
    locale_examples = [
        ("en_GB", "United Kingdom", "London"),
        ("en_US", "United States", "New York"),
        ("de_DE", "Germany", "Berlin"),
        ("fr_FR", "France", "Paris"),
        ("es_ES", "Spain", "Madrid")
    ]
    
    print("Note: Locale is configured in environment variables or server config")
    print("Different locales will search different Careerjet regional sites:")
    
    for locale, country, city in locale_examples:
        print(f"- {locale}: {country} (example search in {city})")


async def main():
    """Run all examples."""
    print("Careerjet MCP Server Usage Examples")
    print("=" * 50)
    
    await example_job_search()
    await example_filtered_search()
    await example_resource_access()
    await example_pagination()
    await example_error_handling()
    await example_cache_management()
    await example_contract_filters()
    await example_locale_usage()
    
    print("\n" + "=" * 50)
    print("Examples completed!")
    print("\nTo use these examples with a real MCP client:")
    print("1. Start the Careerjet MCP Server")
    print("2. Configure your MCP client to connect to the server")
    print("3. Use the client's tool calling and resource reading methods")
    print("4. Handle responses and errors appropriately")


if __name__ == "__main__":
    asyncio.run(main())
