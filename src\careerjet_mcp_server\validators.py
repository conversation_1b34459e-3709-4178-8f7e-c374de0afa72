"""
Input validation utilities for Careerjet MCP Server.
"""

import re
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlparse

from .exceptions import ValidationError


class InputValidator:
    """Utility class for input validation."""
    
    # Valid contract types
    VALID_CONTRACT_TYPES = {'p', 'c', 't', 'i', 'v'}
    
    # Valid contract periods
    VALID_CONTRACT_PERIODS = {'f', 'p'}
    
    # Valid sort options
    VALID_SORT_OPTIONS = {'relevance', 'date', 'salary'}
    
    # Valid locales (subset of most common ones)
    VALID_LOCALES = {
        'en_GB', 'en_US', 'en_CA', 'en_AU', 'en_IN', 'en_ZA',
        'de_DE', 'de_AT', 'de_CH', 'fr_FR', 'fr_CA', 'fr_BE',
        'es_ES', 'es_MX', 'es_AR', 'it_IT', 'pt_PT', 'pt_BR',
        'nl_NL', 'nl_BE', 'sv_SE', 'no_NO', 'da_DK', 'fi_FI',
        'pl_PL', 'ru_RU', 'ja_<PERSON>', 'ko_KR', 'zh_CN'
    }
    
    @staticmethod
    def validate_keywords(keywords: Optional[str]) -> Optional[str]:
        """
        Validate job search keywords.
        
        Args:
            keywords: Keywords to validate
            
        Returns:
            Validated keywords or None
            
        Raises:
            ValidationError: If keywords are invalid
        """
        if keywords is None:
            return None
        
        if not isinstance(keywords, str):
            raise ValidationError("Keywords must be a string", field="keywords", value=keywords)
        
        # Clean and validate keywords
        keywords = keywords.strip()
        
        if len(keywords) == 0:
            return None
        
        if len(keywords) > 500:
            raise ValidationError(
                "Keywords too long (maximum 500 characters)",
                field="keywords",
                value=len(keywords)
            )
        
        # Check for potentially harmful content
        if re.search(r'[<>"\']', keywords):
            raise ValidationError(
                "Keywords contain invalid characters",
                field="keywords",
                value=keywords
            )
        
        return keywords
    
    @staticmethod
    def validate_location(location: Optional[str]) -> Optional[str]:
        """
        Validate job search location.
        
        Args:
            location: Location to validate
            
        Returns:
            Validated location or None
            
        Raises:
            ValidationError: If location is invalid
        """
        if location is None:
            return None
        
        if not isinstance(location, str):
            raise ValidationError("Location must be a string", field="location", value=location)
        
        # Clean and validate location
        location = location.strip()
        
        if len(location) == 0:
            return None
        
        if len(location) > 200:
            raise ValidationError(
                "Location too long (maximum 200 characters)",
                field="location",
                value=len(location)
            )
        
        # Check for potentially harmful content
        if re.search(r'[<>"\']', location):
            raise ValidationError(
                "Location contains invalid characters",
                field="location",
                value=location
            )
        
        return location
    
    @staticmethod
    def validate_sort(sort: str) -> str:
        """
        Validate sort parameter.
        
        Args:
            sort: Sort option to validate
            
        Returns:
            Validated sort option
            
        Raises:
            ValidationError: If sort option is invalid
        """
        if not isinstance(sort, str):
            raise ValidationError("Sort must be a string", field="sort", value=sort)
        
        sort = sort.lower().strip()
        
        if sort not in InputValidator.VALID_SORT_OPTIONS:
            raise ValidationError(
                f"Invalid sort option. Must be one of: {', '.join(InputValidator.VALID_SORT_OPTIONS)}",
                field="sort",
                value=sort
            )
        
        return sort
    
    @staticmethod
    def validate_page(page: int) -> int:
        """
        Validate page number.
        
        Args:
            page: Page number to validate
            
        Returns:
            Validated page number
            
        Raises:
            ValidationError: If page number is invalid
        """
        if not isinstance(page, int):
            raise ValidationError("Page must be an integer", field="page", value=page)
        
        if page < 1:
            raise ValidationError("Page must be >= 1", field="page", value=page)
        
        if page > 1000:  # Reasonable upper limit
            raise ValidationError("Page too high (maximum 1000)", field="page", value=page)
        
        return page
    
    @staticmethod
    def validate_pagesize(pagesize: int) -> int:
        """
        Validate page size.
        
        Args:
            pagesize: Page size to validate
            
        Returns:
            Validated page size
            
        Raises:
            ValidationError: If page size is invalid
        """
        if not isinstance(pagesize, int):
            raise ValidationError("Page size must be an integer", field="pagesize", value=pagesize)
        
        if pagesize < 1:
            raise ValidationError("Page size must be >= 1", field="pagesize", value=pagesize)
        
        if pagesize > 100:
            raise ValidationError("Page size too large (maximum 100)", field="pagesize", value=pagesize)
        
        return pagesize
    
    @staticmethod
    def validate_contract_type(contract_type: Optional[str]) -> Optional[str]:
        """
        Validate contract type.
        
        Args:
            contract_type: Contract type to validate
            
        Returns:
            Validated contract type or None
            
        Raises:
            ValidationError: If contract type is invalid
        """
        if contract_type is None:
            return None
        
        if not isinstance(contract_type, str):
            raise ValidationError(
                "Contract type must be a string",
                field="contract_type",
                value=contract_type
            )
        
        contract_type = contract_type.lower().strip()
        
        if contract_type not in InputValidator.VALID_CONTRACT_TYPES:
            raise ValidationError(
                f"Invalid contract type. Must be one of: {', '.join(InputValidator.VALID_CONTRACT_TYPES)}",
                field="contract_type",
                value=contract_type
            )
        
        return contract_type
    
    @staticmethod
    def validate_contract_period(contract_period: Optional[str]) -> Optional[str]:
        """
        Validate contract period.
        
        Args:
            contract_period: Contract period to validate
            
        Returns:
            Validated contract period or None
            
        Raises:
            ValidationError: If contract period is invalid
        """
        if contract_period is None:
            return None
        
        if not isinstance(contract_period, str):
            raise ValidationError(
                "Contract period must be a string",
                field="contract_period",
                value=contract_period
            )
        
        contract_period = contract_period.lower().strip()
        
        if contract_period not in InputValidator.VALID_CONTRACT_PERIODS:
            raise ValidationError(
                f"Invalid contract period. Must be one of: {', '.join(InputValidator.VALID_CONTRACT_PERIODS)}",
                field="contract_period",
                value=contract_period
            )
        
        return contract_period
    
    @staticmethod
    def validate_locale(locale: str) -> str:
        """
        Validate locale code.
        
        Args:
            locale: Locale code to validate
            
        Returns:
            Validated locale code
            
        Raises:
            ValidationError: If locale is invalid
        """
        if not isinstance(locale, str):
            raise ValidationError("Locale must be a string", field="locale", value=locale)
        
        locale = locale.strip()
        
        if locale not in InputValidator.VALID_LOCALES:
            raise ValidationError(
                f"Invalid locale. Must be one of: {', '.join(sorted(InputValidator.VALID_LOCALES))}",
                field="locale",
                value=locale
            )
        
        return locale
    
    @staticmethod
    def validate_url(url: str) -> str:
        """
        Validate URL format.
        
        Args:
            url: URL to validate
            
        Returns:
            Validated URL
            
        Raises:
            ValidationError: If URL is invalid
        """
        if not isinstance(url, str):
            raise ValidationError("URL must be a string", field="url", value=url)
        
        url = url.strip()
        
        if len(url) == 0:
            raise ValidationError("URL cannot be empty", field="url", value=url)
        
        try:
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                raise ValidationError("Invalid URL format", field="url", value=url)
        except Exception:
            raise ValidationError("Invalid URL format", field="url", value=url)
        
        return url
    
    @staticmethod
    def validate_ip_address(ip: str) -> str:
        """
        Validate IP address format.
        
        Args:
            ip: IP address to validate
            
        Returns:
            Validated IP address
            
        Raises:
            ValidationError: If IP address is invalid
        """
        if not isinstance(ip, str):
            raise ValidationError("IP address must be a string", field="ip", value=ip)
        
        ip = ip.strip()
        
        # Simple IPv4 validation
        if not re.match(r'^(\d{1,3}\.){3}\d{1,3}$', ip):
            raise ValidationError("Invalid IP address format", field="ip", value=ip)
        
        # Check each octet
        octets = ip.split('.')
        for octet in octets:
            if int(octet) > 255:
                raise ValidationError("Invalid IP address", field="ip", value=ip)
        
        return ip
    
    @staticmethod
    def sanitize_string(value: str, max_length: int = 1000) -> str:
        """
        Sanitize string input by removing potentially harmful content.
        
        Args:
            value: String to sanitize
            max_length: Maximum allowed length
            
        Returns:
            Sanitized string
            
        Raises:
            ValidationError: If string is invalid
        """
        if not isinstance(value, str):
            raise ValidationError("Value must be a string", value=value)
        
        # Remove control characters and excessive whitespace
        value = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', value)
        value = re.sub(r'\s+', ' ', value).strip()
        
        if len(value) > max_length:
            raise ValidationError(
                f"String too long (maximum {max_length} characters)",
                value=len(value)
            )
        
        return value
