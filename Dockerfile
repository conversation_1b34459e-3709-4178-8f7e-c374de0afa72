# Dockerfile for Careerjet MCP Server
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies and clean up in one layer
RUN apt-get update && apt-get install -y \
    gcc \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY . .

# Install the package in development mode
RUN pip install -e .

# Create non-root user for security
RUN useradd --create-home --shell /bin/bash app && chown -R app:app /app
USER app

# Expose port for HTTP transport (Smithery requires HTTP)
EXPOSE 8000

# Health check - test HTTP endpoint
HEALTHCHECK --interval=30s --timeout=10s --start-period=15s --retries=3 \
    CMD python health_check.py || exit 1

# Set environment for HTTP transport mode
ENV MCP_TRANSPORT=http
ENV PORT=8000
ENV HOST=0.0.0.0

# Start the MCP server in HTTP mode
CMD ["python", "-m", "careerjet_mcp_server.server"]
