"""
Pytest configuration and fixtures for Careerjet MCP Server tests.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock
from typing import Dict, Any

from src.careerjet_mcp_server.models import CareerjetConfig, JobListing, SearchResults
from src.careerjet_mcp_server.careerjet_client import CareerjetClient


@pytest.fixture
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_config():
    """Create a test configuration."""
    return CareerjetConfig(
        affiliate_id="test123",
        locale="en_GB",
        default_user_agent="Test Agent",
        default_user_ip="127.0.0.1",
        default_url="http://localhost:3000/test",
        rate_limit_requests=10,
        rate_limit_period=60,
        cache_ttl=30,
        timeout=10
    )


@pytest.fixture
def sample_job_listing():
    """Create a sample job listing for testing."""
    return JobListing(
        job_id="12345",
        title="Senior Python Developer",
        company="Tech Corp",
        location="London, UK",
        description="We are looking for an experienced Python developer to join our team.",
        url="https://example.com/job/12345",
        salary="£50,000 - £70,000",
        contract_type="p",
        contract_time="f",
        date_posted="2024-01-15"
    )


@pytest.fixture
def sample_search_results(sample_job_listing):
    """Create sample search results for testing."""
    return SearchResults(
        total_hits=150,
        total_pages=15,
        current_page=1,
        jobs=[sample_job_listing],
        search_params={
            "keywords": "python developer",
            "location": "London",
            "sort": "relevance"
        }
    )


@pytest.fixture
def mock_http_response():
    """Create a mock HTTP response."""
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "hits": 150,
        "pages": 15,
        "jobs": [
            {
                "jobid": "12345",
                "jobtitle": "Senior Python Developer",
                "company": "Tech Corp",
                "locations": "London, UK",
                "jobdescription": "We are looking for an experienced Python developer to join our team.",
                "url": "https://example.com/job/12345",
                "salary": "£50,000 - £70,000",
                "contracttype": "p",
                "contracttime": "f",
                "date": "2024-01-15"
            }
        ]
    }
    mock_response.raise_for_status = MagicMock()
    return mock_response


@pytest.fixture
def mock_http_client(mock_http_response):
    """Create a mock HTTP client."""
    mock_client = AsyncMock()
    mock_client.get.return_value = mock_http_response
    mock_client.aclose = AsyncMock()
    return mock_client


@pytest.fixture
async def careerjet_client(test_config):
    """Create a CareerjetClient instance for testing."""
    client = CareerjetClient(test_config)
    yield client
    # Cleanup if needed
    if client._client:
        await client._client.aclose()


@pytest.fixture
def mock_mcp_context():
    """Create a mock MCP context for testing."""
    context = MagicMock()
    context.info = MagicMock()
    context.error = MagicMock()
    context.warning = MagicMock()
    context.request_context = MagicMock()
    context.request_context.lifespan_context = {}
    return context


@pytest.fixture
def api_error_response():
    """Create a mock API error response."""
    mock_response = MagicMock()
    mock_response.status_code = 400
    mock_response.text = "Bad Request"
    mock_response.json.return_value = {
        "error": "Invalid parameters",
        "error_code": "INVALID_PARAMS"
    }
    return mock_response


@pytest.fixture
def rate_limit_response():
    """Create a mock rate limit response."""
    mock_response = MagicMock()
    mock_response.status_code = 429
    mock_response.text = "Too Many Requests"
    mock_response.headers = {"Retry-After": "60"}
    return mock_response


@pytest.fixture
def network_timeout_error():
    """Create a mock network timeout error."""
    import httpx
    return httpx.TimeoutException("Request timed out")


@pytest.fixture
def connection_error():
    """Create a mock connection error."""
    import httpx
    return httpx.ConnectError("Connection failed")


# Test data fixtures
@pytest.fixture
def valid_search_params():
    """Valid search parameters for testing."""
    return {
        "keywords": "python developer",
        "location": "London",
        "sort": "relevance",
        "page": 1,
        "pagesize": 20,
        "contracttype": "p",
        "contractperiod": "f"
    }


@pytest.fixture
def invalid_search_params():
    """Invalid search parameters for testing."""
    return [
        {"sort": "invalid_sort"},
        {"page": 0},
        {"pagesize": 200},
        {"contracttype": "invalid"},
        {"contractperiod": "invalid"}
    ]


@pytest.fixture
def sample_api_responses():
    """Sample API responses for different scenarios."""
    return {
        "success": {
            "hits": 150,
            "pages": 15,
            "jobs": [
                {
                    "jobid": "12345",
                    "jobtitle": "Senior Python Developer",
                    "company": "Tech Corp",
                    "locations": "London, UK",
                    "jobdescription": "We are looking for an experienced Python developer.",
                    "url": "https://example.com/job/12345",
                    "salary": "£50,000 - £70,000",
                    "contracttype": "p",
                    "contracttime": "f",
                    "date": "2024-01-15"
                }
            ]
        },
        "empty": {
            "hits": 0,
            "pages": 0,
            "jobs": []
        },
        "error": {
            "error": "Invalid affiliate ID",
            "error_code": "INVALID_AFFILIATE"
        }
    }


@pytest.fixture
def locale_endpoints():
    """Sample locale endpoints for testing."""
    return {
        "en_GB": "http://www.careerjet.co.uk",
        "en_US": "http://www.careerjet.com",
        "de_DE": "http://www.careerjet.de",
        "fr_FR": "http://www.optioncarriere.com"
    }
