"""
Custom exceptions for Careerjet MCP Server.
"""

from typing import Any, Dict, Optional


class CareerjetMCPError(Exception):
    """Base exception for Careerjet MCP Server errors."""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary representation."""
        return {
            "error": self.message,
            "error_code": self.error_code,
            "details": self.details
        }


class ValidationError(CareerjetMCPError):
    """Exception raised for input validation errors."""
    
    def __init__(self, message: str, field: Optional[str] = None, value: Any = None):
        super().__init__(message, error_code="VALIDATION_ERROR")
        self.field = field
        self.value = value
        if field:
            self.details["field"] = field
        if value is not None:
            self.details["value"] = value


class APIError(CareerjetMCPError):
    """Exception raised for Careerjet API errors."""
    
    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response_data: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, error_code="API_ERROR")
        self.status_code = status_code
        self.response_data = response_data or {}
        if status_code:
            self.details["status_code"] = status_code
        if response_data:
            self.details["response_data"] = response_data


class RateLimitError(CareerjetMCPError):
    """Exception raised when rate limit is exceeded."""
    
    def __init__(self, message: str, retry_after: Optional[int] = None):
        super().__init__(message, error_code="RATE_LIMIT_ERROR")
        self.retry_after = retry_after
        if retry_after:
            self.details["retry_after"] = retry_after


class ConfigurationError(CareerjetMCPError):
    """Exception raised for configuration errors."""
    
    def __init__(self, message: str, config_key: Optional[str] = None):
        super().__init__(message, error_code="CONFIGURATION_ERROR")
        self.config_key = config_key
        if config_key:
            self.details["config_key"] = config_key


class CacheError(CareerjetMCPError):
    """Exception raised for cache-related errors."""
    
    def __init__(self, message: str, cache_key: Optional[str] = None):
        super().__init__(message, error_code="CACHE_ERROR")
        self.cache_key = cache_key
        if cache_key:
            self.details["cache_key"] = cache_key


class NetworkError(CareerjetMCPError):
    """Exception raised for network-related errors."""
    
    def __init__(self, message: str, url: Optional[str] = None, timeout: Optional[bool] = False):
        super().__init__(message, error_code="NETWORK_ERROR")
        self.url = url
        self.timeout = timeout
        if url:
            self.details["url"] = url
        if timeout:
            self.details["timeout"] = timeout


class JobNotFoundError(CareerjetMCPError):
    """Exception raised when a specific job is not found."""
    
    def __init__(self, job_id: str):
        message = f"Job not found: {job_id}"
        super().__init__(message, error_code="JOB_NOT_FOUND")
        self.job_id = job_id
        self.details["job_id"] = job_id


class InvalidLocaleError(CareerjetMCPError):
    """Exception raised for invalid locale codes."""
    
    def __init__(self, locale: str, valid_locales: Optional[list] = None):
        message = f"Invalid locale: {locale}"
        if valid_locales:
            message += f". Valid locales: {', '.join(valid_locales[:10])}..."
        super().__init__(message, error_code="INVALID_LOCALE")
        self.locale = locale
        self.valid_locales = valid_locales or []
        self.details["locale"] = locale
        if valid_locales:
            self.details["valid_locales"] = valid_locales
