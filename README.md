# Careerjet MCP Server

A Model Context Protocol (MCP) server that provides job search functionality through the Careerjet API. This server enables AI assistants to search for jobs, filter results, and access job listings from Careerjet's global job board network.

## Features

- **Job Search**: Search for jobs by keywords, location, and various filters
- **Advanced Filtering**: Filter by company names, exclude keywords, remote jobs only
- **Multiple Locales**: Support for 40+ locales and countries
- **Pagination**: Handle large result sets with pagination
- **Caching**: Built-in request caching to improve performance
- **Rate Limiting**: Automatic rate limiting to respect API limits
- **MCP Resources**: URI-based access to job data for LLM context
- **Error Handling**: Comprehensive error handling and validation

## Installation

### Prerequisites

- Python 3.8 or higher
- pip package manager

### Install from Source

1. Clone the repository:
```bash
git clone <repository-url>
cd careerjet-mcp-server
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Install the package:
```bash
pip install -e .
```

### Environment Configuration

1. Copy the example environment file:
```bash
cp .env.example .env
```

2. Edit `.env` with your configuration:
```env
CAREERJET_AFFILIATE_ID=371d48447450886ce16b718533cca6f2
CAREERJET_DEFAULT_LOCALE=en_GB
CAREERJET_DEFAULT_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
CAREERJET_DEFAULT_USER_IP=127.0.0.1
CAREERJET_DEFAULT_URL=http://localhost:3000/jobs
CAREERJET_RATE_LIMIT_REQUESTS=100
CAREERJET_RATE_LIMIT_PERIOD=3600
CAREERJET_CACHE_TTL=300
CAREERJET_TIMEOUT=30
```

## Usage

### Starting the Server

Run the MCP server:
```bash
careerjet-mcp-server
```

Or using Python module:
```bash
python -m careerjet_mcp_server.server
```

### MCP Client Integration

Add to your MCP client configuration:

```json
{
  "mcpServers": {
    "careerjet": {
      "command": "careerjet-mcp-server",
      "env": {
        "CAREERJET_AFFILIATE_ID": "371d48447450886ce16b718533cca6f2"
      }
    }
  }
}
```

## Available Tools

### 1. Search Jobs

Search for jobs with basic parameters:

```python
# Example usage in MCP client
result = await client.call_tool("search_jobs", {
    "keywords": "python developer",
    "location": "London",
    "sort": "relevance",
    "page": 1,
    "pagesize": 20,
    "contracttype": "p",  # permanent
    "contractperiod": "f"  # full-time
})
```

**Parameters:**
- `keywords` (optional): Keywords to search for
- `location` (optional): Location to search in
- `sort`: Sort order ("relevance", "date", "salary")
- `page`: Page number (default: 1)
- `pagesize`: Results per page (1-100, default: 20)
- `contracttype`: Contract type ("p", "c", "t", "i", "v")
- `contractperiod`: Contract period ("f", "p")

### 2. Search Jobs with Advanced Filters

Search with additional filtering options:

```python
result = await client.call_tool("search_jobs_filtered", {
    "search": {
        "keywords": "software engineer",
        "location": "San Francisco",
        "sort": "salary"
    },
    "filters": {
        "company_names": ["Google", "Apple", "Microsoft"],
        "exclude_keywords": ["junior", "intern"],
        "remote_only": true
    }
})
```

### 3. Get Job Summary

Get details for a specific job (limited functionality):

```python
result = await client.call_tool("get_job_summary", {
    "job_id": "12345"
})
```

### 4. Clear Search Cache

Clear the internal cache:

```python
result = await client.call_tool("clear_cache")
```

### 5. Get Cache Statistics

Get cache usage statistics:

```python
result = await client.call_tool("get_cache_stats")
```

## Available Resources

### Job Search Results

Access job search results as resources for LLM context:

- `careerjet://search/{keywords}` - Search results for keywords
- `careerjet://search/{keywords}/location/{location}` - Search with location
- `careerjet://recent/{location}` - Recent jobs in location
- `careerjet://config` - Server configuration information

Example:
```python
# Access search results as a resource
content = await client.read_resource("careerjet://search/python developer")
```

## Configuration Options

### Contract Types

- `p`: Permanent
- `c`: Contract
- `t`: Temporary
- `i`: Training
- `v`: Voluntary

### Contract Periods

- `f`: Full-time
- `p`: Part-time

### Sort Options

- `relevance`: Sort by relevance (default)
- `date`: Sort by posting date
- `salary`: Sort by salary

### Supported Locales

The server supports 40+ locales including:
- `en_GB`: United Kingdom
- `en_US`: United States
- `en_CA`: Canada
- `en_AU`: Australia
- `de_DE`: Germany
- `fr_FR`: France
- `es_ES`: Spain
- `it_IT`: Italy
- And many more...

## Error Handling

The server provides comprehensive error handling:

- **Validation Errors**: Invalid input parameters
- **API Errors**: Careerjet API failures
- **Rate Limit Errors**: When rate limits are exceeded
- **Network Errors**: Connection and timeout issues
- **Configuration Errors**: Invalid configuration settings

## Development

### Project Structure

```
src/careerjet_mcp_server/
├── __init__.py          # Package initialization
├── server.py            # Main MCP server implementation
├── careerjet_client.py  # Careerjet API client
├── models.py            # Data models and schemas
├── config.py            # Configuration management
├── exceptions.py        # Custom exceptions
└── validators.py        # Input validation utilities
```

### Running Tests

```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
pytest tests/
```

### Code Quality

The project uses several code quality tools:

```bash
# Format code
black src/ tests/

# Sort imports
isort src/ tests/

# Lint code
ruff check src/ tests/

# Type checking
mypy src/
```

## API Reference

### JobListing Model

```python
class JobListing:
    job_id: str
    title: str
    company: str
    location: str
    description: str
    url: str
    salary: Optional[str]
    contract_type: Optional[str]
    contract_time: Optional[str]
    date_posted: Optional[str]
```

### SearchResults Model

```python
class SearchResults:
    total_hits: int
    total_pages: int
    current_page: int
    jobs: List[JobListing]
    search_params: Dict[str, Any]
    has_more_pages: bool
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
1. Check the documentation
2. Search existing issues
3. Create a new issue with detailed information

## Changelog

### Version 0.1.0
- Initial release
- Basic job search functionality
- MCP server implementation
- Careerjet API integration
- Caching and rate limiting
- Comprehensive error handling
