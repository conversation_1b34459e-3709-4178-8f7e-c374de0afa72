version: 1
runtime: "container"

startCommand:
  type: "http"
  configSchema:
    type: "object"
    required: ["affiliate_id"]
    properties:
      affiliate_id:
        type: "string"
        title: "Careerjet Affiliate ID"
        description: "Your Careerjet affiliate ID for API access"
        default: "371d48447450886ce16b718533cca6f2"
      locale:
        type: "string"
        title: "Default Locale"
        description: "Default locale for job searches (e.g., en_GB, en_US, de_DE)"
        default: "en_GB"
        enum: ["en_GB", "en_US", "en_CA", "en_AU", "de_DE", "fr_FR", "es_ES", "it_IT", "nl_NL", "pl_PL", "pt_BR", "ru_RU", "zh_CN", "ja_JP", "ko_KR"]
      user_agent:
        type: "string"
        title: "User Agent"
        description: "User agent string for API requests"
        default: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
      user_ip:
        type: "string"
        title: "User IP Address"
        description: "IP address for API requests (use your actual IP for production)"
        default: "127.0.0.1"
      base_url:
        type: "string"
        title: "Base URL"
        description: "Base URL for your application (used in API requests)"
        default: "https://your-app.com"
      rate_limit_requests:
        type: "integer"
        title: "Rate Limit - Requests"
        description: "Maximum number of requests per period"
        default: 100
        minimum: 1
        maximum: 1000
      rate_limit_period:
        type: "integer"
        title: "Rate Limit - Period (seconds)"
        description: "Time period for rate limiting in seconds"
        default: 3600
        minimum: 60
        maximum: 86400
      cache_ttl:
        type: "integer"
        title: "Cache TTL (seconds)"
        description: "Time-to-live for cached responses in seconds"
        default: 300
        minimum: 60
        maximum: 3600
      timeout:
        type: "integer"
        title: "Request Timeout (seconds)"
        description: "Timeout for API requests in seconds"
        default: 30
        minimum: 5
        maximum: 120
  exampleConfig:
    affiliate_id: "371d48447450886ce16b718533cca6f2"
    locale: "en_GB"
    user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    user_ip: "127.0.0.1"
    base_url: "https://your-app.com"
    rate_limit_requests: 100
    rate_limit_period: 3600
    cache_ttl: 300
    timeout: 30

build:
  dockerfile: "Dockerfile"
  dockerBuildPath: "."

env:
  PYTHONPATH: "/app/src"
  PYTHONUNBUFFERED: "1"
