"""
Tests for input validation utilities.
"""

import pytest
from src.careerjet_mcp_server.validators import InputValidator
from src.careerjet_mcp_server.exceptions import ValidationError


class TestInputValidator:
    """Test cases for InputValidator class."""
    
    def test_validate_keywords_valid(self):
        """Test valid keywords validation."""
        # Valid keywords
        assert InputValidator.validate_keywords("python developer") == "python developer"
        assert InputValidator.validate_keywords("  software engineer  ") == "software engineer"
        assert InputValidator.validate_keywords(None) is None
        assert InputValidator.validate_keywords("") is None
        assert InputValidator.validate_keywords("   ") is None
    
    def test_validate_keywords_invalid(self):
        """Test invalid keywords validation."""
        # Non-string input
        with pytest.raises(ValidationError) as exc_info:
            InputValidator.validate_keywords(123)
        assert "must be a string" in str(exc_info.value)
        
        # Too long
        with pytest.raises(ValidationError) as exc_info:
            InputValidator.validate_keywords("x" * 501)
        assert "too long" in str(exc_info.value)
        
        # Invalid characters
        with pytest.raises(ValidationError) as exc_info:
            InputValidator.validate_keywords("python<script>")
        assert "invalid characters" in str(exc_info.value)
    
    def test_validate_location_valid(self):
        """Test valid location validation."""
        assert InputValidator.validate_location("London") == "London"
        assert InputValidator.validate_location("  New York  ") == "New York"
        assert InputValidator.validate_location(None) is None
        assert InputValidator.validate_location("") is None
    
    def test_validate_location_invalid(self):
        """Test invalid location validation."""
        # Non-string input
        with pytest.raises(ValidationError):
            InputValidator.validate_location(123)
        
        # Too long
        with pytest.raises(ValidationError):
            InputValidator.validate_location("x" * 201)
        
        # Invalid characters
        with pytest.raises(ValidationError):
            InputValidator.validate_location("London<script>")
    
    def test_validate_sort_valid(self):
        """Test valid sort validation."""
        assert InputValidator.validate_sort("relevance") == "relevance"
        assert InputValidator.validate_sort("date") == "date"
        assert InputValidator.validate_sort("salary") == "salary"
        assert InputValidator.validate_sort("  RELEVANCE  ") == "relevance"
    
    def test_validate_sort_invalid(self):
        """Test invalid sort validation."""
        # Non-string input
        with pytest.raises(ValidationError):
            InputValidator.validate_sort(123)
        
        # Invalid sort option
        with pytest.raises(ValidationError) as exc_info:
            InputValidator.validate_sort("invalid")
        assert "Invalid sort option" in str(exc_info.value)
    
    def test_validate_page_valid(self):
        """Test valid page validation."""
        assert InputValidator.validate_page(1) == 1
        assert InputValidator.validate_page(100) == 100
        assert InputValidator.validate_page(1000) == 1000
    
    def test_validate_page_invalid(self):
        """Test invalid page validation."""
        # Non-integer input
        with pytest.raises(ValidationError):
            InputValidator.validate_page("1")
        
        # Too small
        with pytest.raises(ValidationError):
            InputValidator.validate_page(0)
        
        # Too large
        with pytest.raises(ValidationError):
            InputValidator.validate_page(1001)
    
    def test_validate_pagesize_valid(self):
        """Test valid pagesize validation."""
        assert InputValidator.validate_pagesize(1) == 1
        assert InputValidator.validate_pagesize(50) == 50
        assert InputValidator.validate_pagesize(100) == 100
    
    def test_validate_pagesize_invalid(self):
        """Test invalid pagesize validation."""
        # Non-integer input
        with pytest.raises(ValidationError):
            InputValidator.validate_pagesize("20")
        
        # Too small
        with pytest.raises(ValidationError):
            InputValidator.validate_pagesize(0)
        
        # Too large
        with pytest.raises(ValidationError):
            InputValidator.validate_pagesize(101)
    
    def test_validate_contract_type_valid(self):
        """Test valid contract type validation."""
        assert InputValidator.validate_contract_type("p") == "p"
        assert InputValidator.validate_contract_type("c") == "c"
        assert InputValidator.validate_contract_type("t") == "t"
        assert InputValidator.validate_contract_type("i") == "i"
        assert InputValidator.validate_contract_type("v") == "v"
        assert InputValidator.validate_contract_type("  P  ") == "p"
        assert InputValidator.validate_contract_type(None) is None
    
    def test_validate_contract_type_invalid(self):
        """Test invalid contract type validation."""
        # Non-string input
        with pytest.raises(ValidationError):
            InputValidator.validate_contract_type(123)
        
        # Invalid contract type
        with pytest.raises(ValidationError) as exc_info:
            InputValidator.validate_contract_type("x")
        assert "Invalid contract type" in str(exc_info.value)
    
    def test_validate_contract_period_valid(self):
        """Test valid contract period validation."""
        assert InputValidator.validate_contract_period("f") == "f"
        assert InputValidator.validate_contract_period("p") == "p"
        assert InputValidator.validate_contract_period("  F  ") == "f"
        assert InputValidator.validate_contract_period(None) is None
    
    def test_validate_contract_period_invalid(self):
        """Test invalid contract period validation."""
        # Non-string input
        with pytest.raises(ValidationError):
            InputValidator.validate_contract_period(123)
        
        # Invalid contract period
        with pytest.raises(ValidationError) as exc_info:
            InputValidator.validate_contract_period("x")
        assert "Invalid contract period" in str(exc_info.value)
    
    def test_validate_locale_valid(self):
        """Test valid locale validation."""
        assert InputValidator.validate_locale("en_GB") == "en_GB"
        assert InputValidator.validate_locale("en_US") == "en_US"
        assert InputValidator.validate_locale("de_DE") == "de_DE"
    
    def test_validate_locale_invalid(self):
        """Test invalid locale validation."""
        # Non-string input
        with pytest.raises(ValidationError):
            InputValidator.validate_locale(123)
        
        # Invalid locale
        with pytest.raises(ValidationError) as exc_info:
            InputValidator.validate_locale("invalid")
        assert "Invalid locale" in str(exc_info.value)
    
    def test_validate_url_valid(self):
        """Test valid URL validation."""
        assert InputValidator.validate_url("http://example.com") == "http://example.com"
        assert InputValidator.validate_url("https://example.com/path") == "https://example.com/path"
        assert InputValidator.validate_url("  http://example.com  ") == "http://example.com"
    
    def test_validate_url_invalid(self):
        """Test invalid URL validation."""
        # Non-string input
        with pytest.raises(ValidationError):
            InputValidator.validate_url(123)
        
        # Empty URL
        with pytest.raises(ValidationError):
            InputValidator.validate_url("")
        
        # Invalid URL format
        with pytest.raises(ValidationError):
            InputValidator.validate_url("not-a-url")
        
        with pytest.raises(ValidationError):
            InputValidator.validate_url("http://")
    
    def test_validate_ip_address_valid(self):
        """Test valid IP address validation."""
        assert InputValidator.validate_ip_address("127.0.0.1") == "127.0.0.1"
        assert InputValidator.validate_ip_address("***********") == "***********"
        assert InputValidator.validate_ip_address("  ********  ") == "********"
    
    def test_validate_ip_address_invalid(self):
        """Test invalid IP address validation."""
        # Non-string input
        with pytest.raises(ValidationError):
            InputValidator.validate_ip_address(123)
        
        # Invalid format
        with pytest.raises(ValidationError):
            InputValidator.validate_ip_address("not-an-ip")
        
        # Invalid octets
        with pytest.raises(ValidationError):
            InputValidator.validate_ip_address("256.1.1.1")
        
        with pytest.raises(ValidationError):
            InputValidator.validate_ip_address("1.2.3")
    
    def test_sanitize_string_valid(self):
        """Test valid string sanitization."""
        assert InputValidator.sanitize_string("hello world") == "hello world"
        assert InputValidator.sanitize_string("  hello   world  ") == "hello world"
        assert InputValidator.sanitize_string("hello\nworld") == "hello world"
    
    def test_sanitize_string_invalid(self):
        """Test invalid string sanitization."""
        # Non-string input
        with pytest.raises(ValidationError):
            InputValidator.sanitize_string(123)
        
        # Too long
        with pytest.raises(ValidationError):
            InputValidator.sanitize_string("x" * 1001)
        
        # Custom max length
        with pytest.raises(ValidationError):
            InputValidator.sanitize_string("hello world", max_length=5)
