"""
Tests for data models and schemas.
"""

import pytest
from pydantic import ValidationError
from src.careerjet_mcp_server.models import (
    JobListing,
    SearchResults,
    SearchParameters,
    CareerjetConfig,
    JobSearchFilter
)


class TestJobListing:
    """Test cases for JobListing model."""
    
    def test_job_listing_valid(self):
        """Test valid job listing creation."""
        job_data = {
            "job_id": "12345",
            "title": "Python Developer",
            "company": "Tech Corp",
            "location": "London, UK",
            "description": "We are looking for a Python developer...",
            "url": "https://example.com/job/12345"
        }
        
        job = JobListing(**job_data)
        assert job.job_id == "12345"
        assert job.title == "Python Developer"
        assert job.company == "Tech Corp"
        assert job.location == "London, UK"
        assert job.salary is None
        assert job.contract_type is None
    
    def test_job_listing_with_optional_fields(self):
        """Test job listing with optional fields."""
        job_data = {
            "job_id": "12345",
            "title": "Senior Python Developer",
            "company": "Tech Corp",
            "location": "London, UK",
            "description": "We are looking for a senior Python developer...",
            "url": "https://example.com/job/12345",
            "salary": "£50,000 - £70,000",
            "contract_type": "p",
            "contract_time": "f",
            "date_posted": "2024-01-15"
        }
        
        job = JobListing(**job_data)
        assert job.salary == "£50,000 - £70,000"
        assert job.contract_type == "p"
        assert job.contract_time == "f"
        assert job.date_posted == "2024-01-15"
    
    def test_job_listing_description_cleaning(self):
        """Test description HTML cleaning."""
        job_data = {
            "job_id": "12345",
            "title": "Python Developer",
            "company": "Tech Corp",
            "location": "London, UK",
            "description": "<p>We are looking for a <strong>Python developer</strong>...</p>   \n\n  ",
            "url": "https://example.com/job/12345"
        }
        
        job = JobListing(**job_data)
        assert "<p>" not in job.description
        assert "<strong>" not in job.description
        assert job.description == "We are looking for a Python developer..."
    
    def test_job_listing_missing_required_fields(self):
        """Test job listing with missing required fields."""
        with pytest.raises(ValidationError):
            JobListing(title="Python Developer")  # Missing required fields


class TestSearchResults:
    """Test cases for SearchResults model."""
    
    def test_search_results_valid(self):
        """Test valid search results creation."""
        job_data = {
            "job_id": "12345",
            "title": "Python Developer",
            "company": "Tech Corp",
            "location": "London, UK",
            "description": "We are looking for a Python developer...",
            "url": "https://example.com/job/12345"
        }
        
        job = JobListing(**job_data)
        
        results_data = {
            "total_hits": 100,
            "total_pages": 10,
            "current_page": 1,
            "jobs": [job],
            "search_params": {"keywords": "python"}
        }
        
        results = SearchResults(**results_data)
        assert results.total_hits == 100
        assert results.total_pages == 10
        assert results.current_page == 1
        assert len(results.jobs) == 1
        assert results.has_more_pages is True
    
    def test_search_results_no_more_pages(self):
        """Test search results with no more pages."""
        results_data = {
            "total_hits": 10,
            "total_pages": 1,
            "current_page": 1,
            "jobs": [],
            "search_params": {}
        }
        
        results = SearchResults(**results_data)
        assert results.has_more_pages is False


class TestSearchParameters:
    """Test cases for SearchParameters model."""
    
    def test_search_parameters_valid(self):
        """Test valid search parameters creation."""
        params_data = {
            "keywords": "python developer",
            "location": "London",
            "sort": "relevance",
            "affid": "test123",
            "user_ip": "127.0.0.1",
            "user_agent": "Mozilla/5.0",
            "url": "http://example.com"
        }
        
        params = SearchParameters(**params_data)
        assert params.keywords == "python developer"
        assert params.location == "London"
        assert params.sort == "relevance"
        assert params.start_num == 1
        assert params.pagesize == 20
    
    def test_search_parameters_validation(self):
        """Test search parameters validation."""
        # Invalid sort
        with pytest.raises(ValidationError) as exc_info:
            SearchParameters(
                sort="invalid",
                affid="test123",
                user_ip="127.0.0.1",
                user_agent="Mozilla/5.0",
                url="http://example.com"
            )
        assert "Sort must be one of" in str(exc_info.value)
        
        # Invalid contract type
        with pytest.raises(ValidationError) as exc_info:
            SearchParameters(
                contracttype="invalid",
                affid="test123",
                user_ip="127.0.0.1",
                user_agent="Mozilla/5.0",
                url="http://example.com"
            )
        assert "Contract type must be one of" in str(exc_info.value)
        
        # Invalid page size
        with pytest.raises(ValidationError) as exc_info:
            SearchParameters(
                pagesize=200,
                affid="test123",
                user_ip="127.0.0.1",
                user_agent="Mozilla/5.0",
                url="http://example.com"
            )
        assert "Page size must be between 1 and 100" in str(exc_info.value)


class TestCareerjetConfig:
    """Test cases for CareerjetConfig model."""
    
    def test_config_valid(self):
        """Test valid configuration creation."""
        config_data = {
            "affiliate_id": "test123",
            "locale": "en_GB"
        }
        
        config = CareerjetConfig(**config_data)
        assert config.affiliate_id == "test123"
        assert config.locale == "en_GB"
        assert config.rate_limit_requests == 100
        assert config.cache_ttl == 300
    
    def test_config_invalid_locale(self):
        """Test configuration with invalid locale."""
        with pytest.raises(ValidationError) as exc_info:
            CareerjetConfig(
                affiliate_id="test123",
                locale="invalid_locale"
            )
        assert "Invalid locale" in str(exc_info.value)
    
    def test_config_defaults(self):
        """Test configuration with default values."""
        config = CareerjetConfig(affiliate_id="test123")
        assert config.locale == "en_GB"
        assert config.default_user_agent.startswith("Mozilla/5.0")
        assert config.default_user_ip == "127.0.0.1"
        assert config.rate_limit_requests == 100
        assert config.rate_limit_period == 3600


class TestJobSearchFilter:
    """Test cases for JobSearchFilter model."""
    
    def test_filter_valid(self):
        """Test valid filter creation."""
        filter_data = {
            "min_salary": 50000,
            "max_salary": 100000,
            "company_names": ["Google", "Apple"],
            "exclude_keywords": ["junior", "intern"],
            "remote_only": True
        }
        
        filters = JobSearchFilter(**filter_data)
        assert filters.min_salary == 50000
        assert filters.max_salary == 100000
        assert filters.company_names == ["Google", "Apple"]
        assert filters.exclude_keywords == ["junior", "intern"]
        assert filters.remote_only is True
    
    def test_filter_empty(self):
        """Test empty filter creation."""
        filters = JobSearchFilter()
        assert filters.min_salary is None
        assert filters.max_salary is None
        assert filters.company_names is None
        assert filters.exclude_keywords is None
        assert filters.remote_only is None
